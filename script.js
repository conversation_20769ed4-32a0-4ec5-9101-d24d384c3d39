// Nagdah App JavaScript
document.addEventListener('DOMContentLoaded', function() {
    
    // Bottom Navigation Functionality
    const navItems = document.querySelectorAll('.nav-item');
    
    navItems.forEach(item => {
        item.addEventListener('click', function() {
            // Remove active class from all nav items
            navItems.forEach(nav => nav.classList.remove('active'));
            
            // Add active class to clicked item
            this.classList.add('active');
            
            // Get the navigation text
            const navText = this.querySelector('span').textContent;
            
            // Handle navigation (you can expand this based on your needs)
            handleNavigation(navText);
        });
    });
    
    // Service Card Click Handler
    const serviceCards = document.querySelectorAll('.service-card');
    serviceCards.forEach(card => {
        card.addEventListener('click', function() {
            // Add click animation
            this.style.transform = 'scale(0.98)';
            setTimeout(() => {
                this.style.transform = 'translateY(-2px)';
            }, 150);
            
            // Handle service card click
            const serviceTitle = this.querySelector('.service-title').textContent;
            handleServiceClick(serviceTitle);
        });
    });
    
    // Request Item Click Handler
    const requestItems = document.querySelectorAll('.request-item');
    requestItems.forEach(item => {
        item.addEventListener('click', function() {
            // Add click animation
            this.style.transform = 'scale(0.98)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 150);
            
            // Handle request item click
            const requestNumber = this.querySelector('.request-num').textContent;
            handleRequestClick(requestNumber);
        });
    });
    
    // Order Card Click Handler
    const orderCard = document.querySelector('.order-card');
    if (orderCard) {
        orderCard.addEventListener('click', function() {
            // Add click animation
            this.style.transform = 'scale(0.98)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 150);
            
            // Handle order card click
            const orderNumber = this.querySelector('.order-num').textContent;
            handleOrderClick(orderNumber);
        });
    }
    
    // Progress Bar Animation
    animateProgressBar();
    
    // Add touch feedback for mobile
    addTouchFeedback();
});

// Navigation Handler
function handleNavigation(navText) {
    console.log(`Navigating to: ${navText}`);
    
    // You can implement actual navigation logic here
    switch(navText) {
        case 'الرئيسية':
            // Already on home page
            break;
        case 'طلباتي':
            // Navigate to requests page
            showToast('الانتقال إلى طلباتي');
            break;
        case 'الإشعارات':
            // Navigate to notifications page
            showToast('الانتقال إلى الإشعارات');
            break;
        case 'الملف الشخصي':
            // Navigate to profile page
            showToast('الانتقال إلى الملف الشخصي');
            break;
    }
}

// Service Click Handler
function handleServiceClick(serviceTitle) {
    console.log(`Service clicked: ${serviceTitle}`);
    showToast(`تم النقر على: ${serviceTitle}`);
}

// Request Click Handler
function handleRequestClick(requestNumber) {
    console.log(`Request clicked: #${requestNumber}`);
    showToast(`عرض تفاصيل الطلب #${requestNumber}`);
}

// Order Click Handler
function handleOrderClick(orderNumber) {
    console.log(`Order clicked: #${orderNumber}`);
    showToast(`عرض تفاصيل الطلب #${orderNumber}`);
}

// Progress Bar Animation
function animateProgressBar() {
    const progressFill = document.querySelector('.progress-fill');
    if (progressFill) {
        // Start with 0 width
        progressFill.style.width = '0%';
        
        // Animate to 60% over 2 seconds
        setTimeout(() => {
            progressFill.style.transition = 'width 2s ease-in-out';
            progressFill.style.width = '60%';
        }, 500);
    }
}

// Add Touch Feedback
function addTouchFeedback() {
    const clickableElements = document.querySelectorAll('.service-card, .request-item, .order-card, .nav-item');
    
    clickableElements.forEach(element => {
        element.addEventListener('touchstart', function() {
            this.style.opacity = '0.7';
        });
        
        element.addEventListener('touchend', function() {
            this.style.opacity = '1';
        });
        
        element.addEventListener('touchcancel', function() {
            this.style.opacity = '1';
        });
    });
}

// Toast Notification Function
function showToast(message) {
    // Remove existing toast if any
    const existingToast = document.querySelector('.toast');
    if (existingToast) {
        existingToast.remove();
    }
    
    // Create toast element
    const toast = document.createElement('div');
    toast.className = 'toast';
    toast.textContent = message;
    
    // Toast styles
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        background-color: #333;
        color: white;
        padding: 12px 24px;
        border-radius: 24px;
        font-size: 14px;
        z-index: 1000;
        opacity: 0;
        transition: opacity 0.3s ease;
        max-width: 300px;
        text-align: center;
        font-family: 'Noto Sans Arabic', sans-serif;
    `;
    
    // Add to DOM
    document.body.appendChild(toast);
    
    // Show toast
    setTimeout(() => {
        toast.style.opacity = '1';
    }, 100);
    
    // Hide and remove toast after 3 seconds
    setTimeout(() => {
        toast.style.opacity = '0';
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }, 3000);
}

// Utility function to format Arabic numbers
function formatArabicNumber(number) {
    const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    return number.toString().replace(/[0-9]/g, function(w) {
        return arabicNumbers[+w];
    });
}

// Handle window resize for responsive design
window.addEventListener('resize', function() {
    // Adjust layout if needed
    const appContainer = document.querySelector('.app-container');
    if (window.innerWidth > 375) {
        appContainer.style.marginTop = '20px';
    } else {
        appContainer.style.marginTop = '0';
    }
});

// Initialize app
function initializeApp() {
    console.log('Nagdah App Initialized');
    
    // Add any initialization logic here
    // For example, loading user data, checking authentication, etc.
    
    // Simulate loading state
    setTimeout(() => {
        document.body.classList.add('app-loaded');
    }, 500);
}

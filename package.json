{"name": "nagdah-app", "version": "1.0.0", "description": "Pixel-perfect mobile responsive web interface for Nagdah app", "main": "index.html", "scripts": {"start": "npx http-server . -p 3000 -o", "dev": "npx live-server --port=3000 --open=/", "build": "echo 'No build process needed for static HTML/CSS/JS'", "test": "echo 'No tests specified'"}, "keywords": ["nagdah", "mobile", "responsive", "arabic", "rtl", "web-app"], "author": "Augment Agent", "license": "MIT", "devDependencies": {"http-server": "^14.1.1", "live-server": "^1.2.2"}, "repository": {"type": "git", "url": "."}, "browserslist": ["> 1%", "last 2 versions", "not dead", "iOS >= 10", "Android >= 6"]}
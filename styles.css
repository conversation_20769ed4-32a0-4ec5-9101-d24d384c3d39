/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Noto Sans Arabic', sans-serif;
    background-color: #f5f5f5;
    color: #333;
    direction: rtl;
    text-align: right;
    line-height: 1.6;
    overflow-x: hidden;
}

.app-container {
    max-width: 375px;
    margin: 0 auto;
    background-color: #f5f5f5;
    min-height: 100vh;
    position: relative;
    padding-bottom: 80px;
}

/* Header Styles */
.app-header {
    background-color: #fff;
    padding: 16px 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header-content {
    display: flex;
    justify-content: flex-end;
    align-items: center;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 8px;
}

.logo-icon {
    width: 32px;
    height: 32px;
    background-color: #e91e63;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
}

.logo-text {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
}

.logo-arabic {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    line-height: 1;
}

.logo-english {
    font-size: 12px;
    color: #666;
    line-height: 1;
}

/* Welcome Section */
.welcome-section {
    padding: 20px;
    text-align: center;
}

.welcome-text {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 4px;
}

.welcome-subtitle {
    font-size: 14px;
    color: #666;
}

/* Active Order Section */
.active-order {
    padding: 0 20px 16px;
}

.order-card {
    background-color: #fff;
    border-radius: 12px;
    padding: 16px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.order-details {
    font-size: 14px;
    color: #666;
}

.order-number {
    display: flex;
    align-items: center;
    gap: 4px;
    position: relative;
}

.order-text {
    font-size: 14px;
    color: #333;
}

.order-num {
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.notification-dot {
    width: 8px;
    height: 8px;
    background-color: #e91e63;
    border-radius: 50%;
    position: absolute;
    top: -2px;
    left: -8px;
}

.order-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.order-status {
    font-size: 14px;
    color: #333;
    font-weight: 500;
}

.order-date {
    font-size: 12px;
    color: #666;
}

/* Service Banner */
.service-banner {
    margin: 0 20px 20px;
    background: linear-gradient(135deg, #b3e5fc 0%, #81d4fa 100%);
    border-radius: 12px;
    padding: 16px;
    position: relative;
    overflow: hidden;
}

.banner-content {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
}

.banner-icon {
    color: #0277bd;
    font-size: 16px;
}

.banner-text {
    font-size: 14px;
    color: #0277bd;
    font-weight: 500;
}

.progress-bar {
    width: 100%;
    height: 4px;
    background-color: rgba(255,255,255,0.3);
    border-radius: 2px;
    overflow: hidden;
}

.progress-fill {
    width: 60%;
    height: 100%;
    background-color: #0277bd;
    border-radius: 2px;
}

/* Services Section */
.services-section {
    padding: 0 20px 20px;
}

.section-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 16px;
}

.service-card {
    background-color: #fff;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    cursor: pointer;
    transition: transform 0.2s ease;
}

.service-card:hover {
    transform: translateY(-2px);
}

.service-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.service-info {
    flex: 1;
}

.service-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

.service-description {
    font-size: 14px;
    color: #666;
    line-height: 1.5;
}

.service-icon {
    width: 48px;
    height: 48px;
    background-color: #e91e63;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    margin-left: 16px;
}

/* Incomplete Requests Section */
.incomplete-section {
    padding: 0 20px 20px;
}

.request-item {
    background-color: #fff;
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.request-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.request-details {
    font-size: 14px;
    color: #666;
}

.request-number {
    display: flex;
    align-items: center;
    gap: 4px;
    position: relative;
}

.request-text {
    font-size: 14px;
    color: #333;
}

.request-num {
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.request-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.request-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.request-type {
    font-size: 14px;
    color: #333;
    font-weight: 500;
}

.request-status.valid {
    font-size: 12px;
    color: #4caf50;
    font-weight: 500;
}

.request-location {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    text-align: left;
}

.location-text {
    font-size: 12px;
    color: #666;
}

.location-details {
    font-size: 12px;
    color: #666;
}

/* Bottom Navigation */
.bottom-nav {
    position: fixed;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    max-width: 375px;
    background-color: #fff;
    border-top: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-around;
    padding: 12px 0 8px;
    box-shadow: 0 -2px 8px rgba(0,0,0,0.1);
}

.nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    cursor: pointer;
    transition: color 0.2s ease;
    color: #999;
}

.nav-item.active {
    color: #e91e63;
}

.nav-item i {
    font-size: 20px;
}

.nav-item span {
    font-size: 10px;
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 375px) {
    .app-container {
        max-width: 100%;
    }
    
    .bottom-nav {
        max-width: 100%;
    }
}

@media (min-width: 376px) {
    .app-container {
        border: 1px solid #e0e0e0;
        border-radius: 12px;
        margin-top: 20px;
        overflow: hidden;
    }
}
